import { Router } from "express";
import { authMiddleware } from "../../middlewares/auth";
import { forceQuitMiddleware } from "../../middlewares/forceQuite";
import { User } from "../../Models/User";
import { roles } from "../../utils/roles";
import { QUERY_PAGE_SIZE_LIMIT } from "../../utils/FLAGS";
import { signWorkerInviteToken, verifyWorkerInviteToken } from "../../utils/workerInviteToken";
import { sendInviteEmail } from "../../utils/mailer";
import { Facility } from "../../Models/Facility";

const router = Router();

// Public route: GET /facility/workers/join?token=...
router.get("/workers/join", async (req, res) => {
  try {
    const { token } = req.query as { token?: string };
    if (!token) {
      res.status(400).json({ error: "Missing token" });
      return;
    }
    const payload = verifyWorkerInviteToken(token);
    const { userId, facilityId, role, specialization } = payload;

    if (![roles.admin, roles.doctor].includes(role)) {
      res.status(400).json({ error: "Invalid role in token" });
      return;
    }

    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }

    const exists = user.profiles.find(
      (p) => p.type === role && p.hospital?.toString() === facilityId.toString()
    );
    if (exists) {
      res.json({ message: "Already joined" });
      return;
    }

    user.profiles.push({
      type: role,
      hospital: (facilityId as unknown) as any,
      specialization: role === roles.doctor ? (specialization || "") : "",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    user.updatedAt = Date.now();
    await user.save();

    res.json({ message: "Joined successfully" });
  } catch (e: any) {
    if (e?.name === "TokenExpiredError") {
      res.status(400).json({ error: "Token expired" });
      return;
    }
    res.status(400).json({ error: "Invalid token" });
  }
});

// Authenticated routes below
// Dashboard metrics for Facility Manager
router.get(
  "/dashboard/metrics",
  authMiddleware,
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
    sudo: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      // Resolve the facility the manager belongs to
      const facilityManagerUser = await User.findById(req.user?.id).lean();
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }
      const facilityManagerProfile = facilityManagerUser.profiles.find(
        (p) => p.type === roles.facilityManager
      );
      if (!facilityManagerProfile?.hospital) {
        res
          .status(400)
          .json({ error: "Facility manager not associated with a facility" });
        return;
      }
      const facilityId = facilityManagerProfile.hospital;

      // Workers overview (admins + doctors only)
      const [totalWorkers, doctorsCount, adminsCount] = await Promise.all([
        User.countDocuments({
          is_deleted: { $ne: true },
          "profiles.hospital": facilityId,
          "profiles.type": { $in: [roles.admin, roles.doctor] },
        }),
        User.countDocuments({
          is_deleted: { $ne: true },
          "profiles.hospital": facilityId,
          "profiles.type": roles.doctor,
        }),
        User.countDocuments({
          is_deleted: { $ne: true },
          "profiles.hospital": facilityId,
          "profiles.type": roles.admin,
        }),
      ]);

      const doctorsPct = totalWorkers
        ? Math.round((doctorsCount / totalWorkers) * 1000) / 10
        : 0;
      const adminsPct = totalWorkers
        ? Math.round((adminsCount / totalWorkers) * 1000) / 10
        : 0;

      // Determine doctors belonging to this facility to scope patient/records metrics
      const doctorIds = await User.find(
        {
          is_deleted: { $ne: true },
          "profiles.hospital": facilityId,
          "profiles.type": roles.doctor,
        },
        { _id: 1 }
      )
        .lean()
        .then((docs) => docs.map((d) => d._id));

      // Patients associated with this facility are those having treatments with these doctors
      // Total distinct patients for this facility
      const distinctPatientsFilter: any = {
        doctor: { $in: doctorIds },
        is_deleted: { $ne: true },
      };

      // Note: Treatment.start is used as the creation timestamp for a treatment
      const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;

      const [patientsDistinct, patientsDistinctRecent] = await Promise.all([
        // total unique patients
        (await import("../../Models/Treatment")).Treatment.distinct(
          "patient",
          distinctPatientsFilter
        ),
        // unique patients whose first treatment in the last 7 days (approximation: any treatment in last 7d)
        (await import("../../Models/Treatment")).Treatment.distinct("patient", {
          ...distinctPatientsFilter,
          start: { $gte: sevenDaysAgo },
        }),
      ]);

      // Medical records activity: visits in the last 7 days performed by doctors of this facility
      const { Visit } = await import("../../Models/Visit");
      const weeklyUpdatedRecords = await Visit.countDocuments({
        doctor: { $in: doctorIds },
        date: { $gte: sevenDaysAgo },
        is_deleted: { $ne: true },
      });

      res.json({
        patients: {
          total: patientsDistinct.length,
          last7Days: patientsDistinctRecent.length,
        },
        medicalRecords: {
          last7Days: weeklyUpdatedRecords,
        },
        staff: {
          totalWorkers,
          doctors: { count: doctorsCount, percentage: doctorsPct },
          admins: { count: adminsCount, percentage: adminsPct },
        },
      });
    } catch (error) {
      console.error("Error computing facility dashboard metrics:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

router.use(authMiddleware);

// GET /facility/workers - Get workers (admin and doctor roles) for the facility
router.get(
  "/workers",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const { page = 0, search = "", exact = "false" } = req.query;
      const pageNumber = parseInt(page as string);
      const isExact = exact === "true";

      // Get the facility manager's hospital from their profile
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );

      if (!facilityManagerProfile || !facilityManagerProfile.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital;

      // Build search query for workers (admin and doctor roles only)
      const searchQuery: any = {
        is_deleted: { $ne: true },
        "profiles.type": { $in: [roles.admin, roles.doctor] },
        "profiles.hospital": facilityId
      };

      if (search) {
        const searchStr = search as string;
        if (isExact) {
          searchQuery.$or = [
            { cin: searchStr },
            { email: searchStr },
            { phone: searchStr }
          ];
        } else {
          searchQuery.$or = [
            { name: { $regex: searchStr, $options: "i" } },
            { cin: { $regex: searchStr, $options: "i" } },
            { email: { $regex: searchStr, $options: "i" } },
            { phone: { $regex: searchStr, $options: "i" } }
          ];
        }
      }

      const workers = await User.find(searchQuery)
        .skip(pageNumber * QUERY_PAGE_SIZE_LIMIT)
        .limit(QUERY_PAGE_SIZE_LIMIT)
        .select("-password")
        .lean();

      res.json(workers);
    } catch (error) {
      console.error("Error fetching workers:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// GET /facility/workers/search - Advanced search for workers
router.get(
  "/workers/search",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const { cin, name, phone, email, role, specialization, exact = "false" } = req.query;
      const isExact = exact === "true";

      // Get the facility manager's hospital
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );

      if (!facilityManagerProfile || !facilityManagerProfile.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital;

      const searchQuery: any = {
        is_deleted: { $ne: true },
        "profiles.type": { $in: [roles.admin, roles.doctor] },
        "profiles.hospital": facilityId
      };

      if (cin) {
        searchQuery.cin = isExact ? cin : { $regex: cin, $options: "i" };
      }
      if (name) {
        searchQuery.name = isExact ? name : { $regex: name, $options: "i" };
      }
      if (phone) {
        searchQuery.phone = isExact ? phone : { $regex: phone, $options: "i" };
      }
      if (email) {
        searchQuery.email = isExact ? email : { $regex: email, $options: "i" };
      }
      if (role && [roles.admin, roles.doctor].includes(role as string)) {
        searchQuery["profiles.type"] = role;
      }
      if (specialization) {
        searchQuery["profiles.specialization"] = isExact
          ? specialization
          : { $regex: specialization, $options: "i" };
      }

      const workers = await User.find(searchQuery)
        .select("-password")
        .lean();

      res.json(workers);
    } catch (error) {
      console.error("Error in advanced worker search:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// GET /facility/me - Get current facility details for the facility manager
router.get(
  "/me",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
    sudo: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }
      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );
      if (!facilityManagerProfile?.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }
      const facility = await Facility.findById(facilityManagerProfile.hospital).lean();
      if (!facility) {
        res.status(404).json({ error: "Facility not found" });
        return;
      }
      res.json(facility);
    } catch (e) {
      console.error(e);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// PUT /facility/me - Update facility details
router.put(
  "/me",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
    sudo: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }
      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );
      if (!facilityManagerProfile?.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }
      const facilityId = facilityManagerProfile.hospital;

      const allowed: any = {};
      const { name, type, address, phone, img, is_deleted } = req.body;
      if (name !== undefined) allowed.name = name;
      if (type !== undefined) allowed.type = type; // enum validated by schema
      if (address !== undefined) allowed.address = address;
      if (phone !== undefined) allowed.phone = phone;
      if (img !== undefined) allowed.img = img;
      if (is_deleted !== undefined) allowed.is_deleted = is_deleted; // allow soft delete toggle
      allowed.updatedAt = Date.now();

      const updated = await Facility.findByIdAndUpdate(facilityId, allowed, { new: true }).lean();
      if (!updated) {
        res.status(404).json({ error: "Facility not found" });
        return;
      }
      res.json(updated);
    } catch (e) {
      console.error(e);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// POST /facility/workers - Create a new worker (admin or doctor)
router.post(
  "/workers",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
    sudo: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const {
        cin,
        firstName,
        lastName,
        email,
        phone,
        gender,
        birthDate,
        role,
        specialization,
        password
      } = req.body;

      // Validate required fields
      if (!cin || !firstName || !lastName || !email || !gender || !birthDate || !role || !password) {
        res.status(400).json({ error: "Missing required fields" });
        return;
      }

      // Validate role
      if (![roles.admin, roles.doctor].includes(role)) {
        res.status(400).json({ error: "Invalid role. Only admin and doctor roles are allowed" });
        return;
      }

      // Get the facility manager's hospital
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );

      if (!facilityManagerProfile || !facilityManagerProfile.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital;

      // Check if user with this CIN or email already exists
      const existingUser = await User.findOne({
        $or: [{ cin }, { email }],
        is_deleted: { $ne: true }
      });

      if (existingUser) {
        const alreadyInFacility = existingUser.profiles.find(
          (p) => p.type === role && p.hospital?.toString() === facilityId.toString()
        );
        if (alreadyInFacility) {
          res.status(400).json({ error: "User already exists with this role in this facility" });
          return;
        }
        // Send invitation email with 48h token so the user can accept joining
        const token = signWorkerInviteToken({
          userId: existingUser._id.toString(),
          facilityId: facilityId.toString(),
          role: role,
          specialization: role === roles.doctor ? (specialization || "") : undefined,
          inviterId: req.user.id,
        });
        const baseUrl = process.env.APP_BASE_URL || `http://localhost:${process.env.APP_PORT || 4000}`;
        const joinUrl = `${baseUrl}/facility/workers/join?token=${encodeURIComponent(token)}`;
        // Fetch facility name for email context
        let facilityName = "Facility";
        try {
          const facility = await Facility.findById(facilityId).lean();
          if (facility?.name) facilityName = facility.name;
        } catch {}
        try {
          await sendInviteEmail({ to: existingUser.email, facilityName, joinUrl });
        } catch (e) {
          console.error("Failed to send invite email:", e);
          res.status(500).json({ error: "Failed to send invitation email" });
          return;
        }
        res.status(202).json({ message: "User exists. Invitation email sent.", email: existingUser.email });
        return;
      }

      // Create new user
      const newUser = new User({
        name: [firstName, lastName],
        email,
        phone: phone || null,
        img: null,
        gender,
        BirthDay: typeof birthDate === 'number' ? birthDate : new Date(birthDate).getTime(),
        password: require("../../utils/passwordHash").hashPassword(password),
        cin,
        profiles: [{
          type: role,
          hospital: facilityId,
          specialization: role === roles.doctor ? (specialization || "") : "",
          createdAt: Date.now(),
          updatedAt: Date.now(),
        }],
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      await newUser.save();

      // Return the created user without password
      const userResponse = newUser.toObject();
      const { password: _, ...userWithoutPassword } = userResponse;

      res.status(201).json(userWithoutPassword);
    } catch (error) {
      console.error("Error creating worker:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// DELETE /facility/workers/:workerId - Soft delete a worker
router.delete(
  "/workers/:workerId",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
    sudo: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const { workerId } = req.params;

      if (!workerId) {
        res.status(400).json({ error: "Worker ID is required" });
        return;
      }

      // Get the facility manager's hospital
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );

      if (!facilityManagerProfile || !facilityManagerProfile.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital;

      // Find the worker and verify they belong to this facility
      const worker = await User.findById(workerId);
      if (!worker) {
        res.status(404).json({ error: "Worker not found" });
        return;
      }

      // Check if worker belongs to this facility and has admin/doctor role
      const workerProfile = worker.profiles.findIndex(
        p => (p.type === roles.admin || p.type === roles.doctor) &&
            p.hospital?.toString() === facilityId.toString()
      );

      if (workerProfile === -1) {
        res.status(403).json({ error: "Worker does not belong to your facility or is not a valid worker" });
        return;
      }

      worker.profiles.splice(workerProfile, 1);
      worker.updatedAt = Date.now();
      await worker.save();

      res.json({ message: "Worker deleted successfully" });
    } catch (error) {
      console.error("Error deleting worker:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

router.post("/", async (_req, res) => {
  res.status(501).json({ message: "Not implemented" });
});
export default router;
