import {<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ltip, ResponsiveContainer } from 'recharts';

export default function FacilityDashboard() {
  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">Facility Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <PatientsCard />
        <FacilityStaffCard />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FacilityDataCard />
        <MedicalRecordsChartCard />
      </div>
    </div>
  );
}




function MedicalRecordsChartCard() {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-gray-800 mb-4">Medical Records</h2>
      <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
        <p className="text-gray-500">Chart will be displayed here</p>
      </div>
    </div>
  );
}

function FacilityStaffCard() {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-gray-800 mb-4">Facility Staff</h2>
        <ProgressBarChart />
      
    </div>
  );
}

function FacilityDataCard() {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-gray-800 mb-4">Facility Data</h2>
      <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
        <p className="text-gray-500">Facility data will be displayed here</p>
      </div>
    </div>
  );
}

function PatientsCard() {
  return (
    <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      <div className="px-6 pt-6 pb-2">
        <h2 className="text-xl font-semibold text-gray-800">Patients</h2>
      </div>
      
      <div className="flex justify-between items-center px-6 py-2">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-blue-50 rounded-full">
            <i className="fas fa-user-injured text-blue-600 text-3xl"></i>
          </div>
          <p className="text-4xl font-bold text-gray-800">109</p>
        </div> 
        
        <div className="w-[100px] h-[100px] transform hover:scale-105 transition-transform">
          <SimplePieChart />
        </div>
      </div>
      
      <div className="flex items-center justify-between border-t border-gray-100 bg-gray-50 px-6 py-4 mt-4">
        <p className="text-sm text-gray-600 font-medium">27 in last 7 days</p>
        <div className="flex gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">F</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-pink-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">M</span>
          </div>
        </div>
      </div>
    </div>
  );
}

const data = [
  { name: "FEMALE", value: 500 },
  { name: 'MALE', value: 200 },
];

const COLORS = ['#3b82f6', '#ec4899'];

function SimplePieChart() {
  return (
    <PieChart width={100} height={100}>
      <Pie
        data={data}
        cx="50%"
        cy="50%"
        innerRadius={25}
        outerRadius={40}
        paddingAngle={2}
        dataKey="value"
        animationBegin={100}
        animationDuration={1000}
      >
        {data.map((entry, index) => (
          <Cell 
            key={`cell-${index}`} 
            fill={COLORS[index % COLORS.length]} 
            stroke="transparent"
          />
        ))}
      </Pie>
    </PieChart>
  );
}





const ProgressBarChart = () => {
  
  const data = [
  { 
    name: 'Progress', 
    segment1: 30,  // Represents 30%
    segment2: 20,  // Represents 20%
    segment3: 50,  // Represents 50%
  }
];
  ;return (
  
    <BarChart width={100} height={100}
      data={data}
      layout="horizontal"
      stackOffset="expand"  // Makes bars add up to 100% (like a progress bar)
    >
      <XAxis type="number" domain={[0, 100]}  />  {/* Force 0-100% scale */}
      <YAxis type="category" dataKey="name"  />   {/* Hide Y-axis label */}
      <Tooltip formatter={(value) => `${value}%`} />  {/* Show % in tooltip */}
      <Bar dataKey="segment1" stackId="1" fill="#8884d8" name="Segment 1" />
      <Bar dataKey="segment2" stackId="1" fill="#82ca9d" name="Segment 2" />
      <Bar dataKey="segment3" stackId="1" fill="#ffc658" name="Segment 3" />
    </BarChart>
);}

